import { BadRequestException, Injectable, Logger, OnApplicationBootstrap, Inject, forwardRef } from '@nestjs/common';
import { UserService } from '../user/user.service';
import { EnergyService } from '../user/energy.service';
import { AutoMode } from '../user/enums/auto-mode.enum';
import { AutoActionQueue } from '../queue/auto-action.queue';

@Injectable()
export class BullMQAutoActionService implements OnApplicationBootstrap {
  private readonly MINIMUM_ENERGY_THRESHOLD = 10;
  private readonly logger = new Logger(BullMQAutoActionService.name);

  constructor(
    @Inject(forwardRef(() => UserService))
    private readonly userService: UserService,
    @Inject(forwardRef(() => EnergyService))
    private readonly energyService: EnergyService,
    @Inject(forwardRef(() => AutoActionQueue))
    private readonly autoActionQueue: AutoActionQueue,
  ) {}

  /**
   * Called after the application has fully started.
   * This is where we restore auto actions that were active before server restart.
   */
  async onApplicationBootstrap() {
    this.logger.log('BullMQAutoActionService bootstrap - restoring active auto actions...');

    try {
      await this.restoreAutoActions();
      this.logger.log('Auto actions restoration completed');
    } catch (error) {
      this.logger.error(`Error during auto actions restoration: ${error.message}`, error.stack);
    }
  }

  /**
   * Start an auto action for a user
   */
  async startAutoAction(
    userId: number,
    targetId: string,
    type: AutoMode,
  ): Promise<void> {
    const user = await this.userService.findOne(userId);
    if (!user.isPremium) {
      throw new BadRequestException('Auto mode is only available for premium users');
    }

    // Check if job already exists
    const jobExists = await this.autoActionQueue.jobExists(userId, targetId, type);
    if (jobExists) {
      throw new BadRequestException(`Auto action already running for this ${type}`);
    }

    // Set expiration time - 24 hours for work, null for war (war has its own end time)
    let expiresAt: Date | null = null;
    if (type === AutoMode.WORK) {
      expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 24);
    }

    // Update user with auto mode info
    await this.userService.updateAutoMode(userId, {
      activeAutoMode: type,
      autoTargetId: targetId,
      autoModeExpiresAt: expiresAt,
    });

    // Add job to queue
    await this.autoActionQueue.addAutoActionJob(userId, targetId, type);

    // Execute immediately if user has enough energy
    await this.executeImmediateAction(userId, targetId, type);

    this.logger.log(`Started auto ${type} for user ${userId} on target ${targetId}`);
  }

  /**
   * Stop an auto action for a user
   */
  async stopAutoAction(
    userId: number,
    targetId: string,
    type: AutoMode,
  ): Promise<void> {
    // Remove job from queue
    await this.autoActionQueue.removeAutoActionJob(userId, targetId, type);

    // Update user to clear auto mode
    await this.userService.updateAutoMode(userId, {
      activeAutoMode: AutoMode.NONE,
      autoTargetId: null,
      autoModeExpiresAt: null,
    });

    this.logger.log(`Stopped auto ${type} for user ${userId} on target ${targetId}`);
  }

  /**
   * Execute an action immediately when auto mode is started
   */
  private async executeImmediateAction(
    userId: number,
    targetId: string,
    type: AutoMode,
  ): Promise<void> {
    try {
      // Get the user without updating energy first
      const currentUser = await this.userService.findOne(userId);

      // Calculate current energy without saving
      const currentEnergy = this.energyService.calculateCurrentEnergy(currentUser);

      // Check if user has minimum required energy
      if (currentEnergy >= this.MINIMUM_ENERGY_THRESHOLD) {
        this.logger.log(
          `Executing initial auto ${type} for user ${userId} on target ${targetId} with ${currentEnergy} energy`,
        );

        // The worker will handle the actual execution
        // For now, we just log that we would execute
        this.logger.log(`Initial auto ${type} would execute with ${currentEnergy} energy`);
      } else {
        this.logger.log(
          `Skipping initial auto ${type} for user ${userId} on target ${targetId} - insufficient energy (${currentEnergy}, minimum required: ${this.MINIMUM_ENERGY_THRESHOLD})`,
        );
      }
    } catch (error) {
      this.logger.error(
        `Initial auto ${type} execution failed for user ${userId} on target ${targetId}: ${error.message}`,
      );
    }
  }

  /**
   * Restore auto actions that were active before server restart
   */
  private async restoreAutoActions(): Promise<void> {
    try {
      // Find all users with active auto modes
      const usersWithAutoMode = await this.userService.findUsersWithActiveAutoMode();

      this.logger.log(`Found ${usersWithAutoMode.length} users with active auto modes`);

      for (const user of usersWithAutoMode) {
        try {
          // Check if user is still premium
          if (!user.isPremium) {
            this.logger.log(`User ${user.id} is no longer premium, clearing auto mode`);
            await this.userService.updateAutoMode(user.id, {
              activeAutoMode: AutoMode.NONE,
              autoTargetId: null,
              autoModeExpiresAt: null,
            });
            continue;
          }

          // Check if work auto mode has expired
          if (user.activeAutoMode === AutoMode.WORK && user.autoModeExpiresAt && new Date() > user.autoModeExpiresAt) {
            this.logger.log(`Auto work for user ${user.id} has expired, clearing auto mode`);
            await this.userService.updateAutoMode(user.id, {
              activeAutoMode: AutoMode.NONE,
              autoTargetId: null,
              autoModeExpiresAt: null,
            });
            continue;
          }

          // Check if job already exists in queue
          const jobExists = await this.autoActionQueue.jobExists(
            user.id,
            user.autoTargetId,
            user.activeAutoMode,
          );

          if (!jobExists) {
            // Restore the job
            await this.autoActionQueue.addAutoActionJob(
              user.id,
              user.autoTargetId,
              user.activeAutoMode,
            );

            this.logger.log(
              `Restored auto ${user.activeAutoMode} for user ${user.id} on target ${user.autoTargetId}`,
            );
          } else {
            this.logger.log(
              `Auto ${user.activeAutoMode} job already exists for user ${user.id} on target ${user.autoTargetId}`,
            );
          }
        } catch (error) {
          this.logger.error(
            `Failed to restore auto action for user ${user.id}: ${error.message}`,
          );
        }
      }
    } catch (error) {
      this.logger.error(`Failed to restore auto actions: ${error.message}`);
    }
  }

  /**
   * Get statistics about active auto actions
   */
  async getAutoActionStats() {
    try {
      const queueStats = await this.autoActionQueue.getQueueStats();
      const activeJobs = await this.autoActionQueue.getActiveJobs();

      return {
        queueStats,
        activeJobs: activeJobs.length,
        jobsByType: {
          work: activeJobs.filter(job => job.type === AutoMode.WORK).length,
          war: activeJobs.filter(job => job.type === AutoMode.WAR).length,
        },
      };
    } catch (error) {
      this.logger.error('Failed to get auto action stats', error);
      return {
        queueStats: { waiting: 0, active: 0, completed: 0, failed: 0, repeatable: 0 },
        activeJobs: 0,
        jobsByType: { work: 0, war: 0 },
      };
    }
  }
}
