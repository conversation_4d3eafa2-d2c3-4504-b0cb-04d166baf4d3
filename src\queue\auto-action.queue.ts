import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { AutoMode } from '../user/enums/auto-mode.enum';
import { AutoActionJobData, AutoActionJobOptions } from './dto/auto-action-job.dto';

@Injectable()
export class AutoActionQueue {
  private readonly logger = new Logger(AutoActionQueue.name);

  constructor(
    @InjectQueue('auto-actions')
    private readonly autoActionQueue: Queue<AutoActionJobData>,
  ) {}

  /**
   * Add a new auto action job to the queue
   */
  async addAutoActionJob(
    userId: number,
    targetId: string,
    type: AutoMode,
  ): Promise<void> {
    const jobName = `${userId}_${targetId}_${type}`;
    const jobData: AutoActionJobData = {
      userId,
      targetId,
      type,
    };

    const jobOptions: AutoActionJobOptions = {
      repeat: {
        pattern: '*/1 * * * *', // Every 30 minutes
      },
      removeOnComplete: 10, // Keep last 10 completed jobs
      removeOnFail: 50, // Keep last 50 failed jobs
      attempts: 3, // Retry up to 3 times on failure
      backoff: {
        type: 'exponential',
        delay: 2000, // Start with 2 second delay
      },
    };

    try {
      await this.autoActionQueue.add(jobName, jobData, jobOptions);
      this.logger.log(`Added auto action job: ${jobName}`);
    } catch (error) {
      this.logger.error(`Failed to add auto action job: ${jobName}`, error);
      throw error;
    }
  }

  /**
   * Remove an auto action job from the queue
   */
  async removeAutoActionJob(
    userId: number,
    targetId: string,
    type: AutoMode,
  ): Promise<void> {
    const jobName = `${userId}_${targetId}_${type}`;

    try {
      // Remove repeatable job
      await this.autoActionQueue.removeRepeatable(jobName, {
        pattern: '*/30 * * * *',
      });

      // Also remove any pending jobs with this name
      const jobs = await this.autoActionQueue.getJobs(['waiting', 'delayed']);
      for (const job of jobs) {
        if (job.name === jobName) {
          await job.remove();
        }
      }

      this.logger.log(`Removed auto action job: ${jobName}`);
    } catch (error) {
      this.logger.error(`Failed to remove auto action job: ${jobName}`, error);
      throw error;
    }
  }

  /**
   * Get all active auto action jobs
   */
  async getActiveJobs(): Promise<Array<{ userId: number; targetId: string; type: AutoMode }>> {
    try {
      const repeatableJobs = await this.autoActionQueue.getRepeatableJobs();
      return repeatableJobs.map(job => {
        const [userId, targetId, type] = job.name.split('_');
        return {
          userId: parseInt(userId),
          targetId,
          type: type as AutoMode,
        };
      });
    } catch (error) {
      this.logger.error('Failed to get active jobs', error);
      return [];
    }
  }

  /**
   * Check if a specific auto action job exists
   */
  async jobExists(
    userId: number,
    targetId: string,
    type: AutoMode,
  ): Promise<boolean> {
    const jobName = `${userId}_${targetId}_${type}`;
    
    try {
      const repeatableJobs = await this.autoActionQueue.getRepeatableJobs();
      return repeatableJobs.some(job => job.name === jobName);
    } catch (error) {
      this.logger.error(`Failed to check if job exists: ${jobName}`, error);
      return false;
    }
  }

  /**
   * Get queue statistics
   */
  async getQueueStats() {
    try {
      const waiting = await this.autoActionQueue.getWaiting();
      const active = await this.autoActionQueue.getActive();
      const completed = await this.autoActionQueue.getCompleted();
      const failed = await this.autoActionQueue.getFailed();
      const repeatableJobs = await this.autoActionQueue.getRepeatableJobs();

      return {
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
        repeatable: repeatableJobs.length,
      };
    } catch (error) {
      this.logger.error('Failed to get queue stats', error);
      return {
        waiting: 0,
        active: 0,
        completed: 0,
        failed: 0,
        repeatable: 0,
      };
    }
  }
}
