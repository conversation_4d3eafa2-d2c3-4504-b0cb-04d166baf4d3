import { Module, forwardRef } from '@nestjs/common';
import { AutoActionService } from './auto-action.service';
import { BullMQAutoActionService } from './bullmq-auto-action.service';
import { UserModule } from '../user/user.module';
import { WarModule } from 'src/war/war.module';
import { QueueModule } from '../queue/queue.module';

@Module({
  imports: [
    forwardRef(() => UserModule),
    forwardRef(() => WarModule),
    forwardRef(() => QueueModule),
  ],
  providers: [
    AutoActionService, // Keep the old service for now
    BullMQAutoActionService, // Add the new BullMQ service
  ],
  exports: [
    AutoActionService, // Keep exporting the old service for backward compatibility
    BullMQAutoActionService, // Export the new service
  ],
})
export class SharedModule {}
