import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Injectable, Logger, Inject, forwardRef } from '@nestjs/common';
import { Job } from 'bullmq';
import { AutoActionJobData } from './dto/auto-action-job.dto';
import { AutoMode } from '../user/enums/auto-mode.enum';
import { UserService } from '../user/user.service';
import { EnergyService } from '../user/energy.service';
import { AutoActionQueue } from './auto-action.queue';

@Injectable()
@Processor('auto-actions')
export class AutoActionWorker extends WorkerHost {
  private readonly logger = new Logger(AutoActionWorker.name);
  private readonly MINIMUM_ENERGY_THRESHOLD = 10;

  constructor(
    @Inject(forwardRef(() => UserService))
    private readonly userService: UserService,
    @Inject(forwardRef(() => EnergyService))
    private readonly energyService: EnergyService,
    private readonly autoActionQueue: AutoActionQueue,
  ) {
    super();
  }

  async process(job: Job<AutoActionJobData>): Promise<void> {
    const { userId, targetId, type } = job.data;
    const jobName = `${userId}_${targetId}_${type}`;

    this.logger.log(`Processing auto action job: ${jobName}`);

    try {
      // Check if user still exists and is premium
      const user = await this.userService.findOne(userId);
      if (!user) {
        this.logger.warn(`User ${userId} not found, removing job: ${jobName}`);
        await this.autoActionQueue.removeAutoActionJob(userId, targetId, type);
        return;
      }

      if (!user.isPremium) {
        this.logger.warn(`User ${userId} is no longer premium, removing job: ${jobName}`);
        await this.autoActionQueue.removeAutoActionJob(userId, targetId, type);
        return;
      }

      // Check if auto mode is still active for this user
      if (user.activeAutoMode !== type || user.autoTargetId !== targetId) {
        this.logger.log(`Auto mode changed for user ${userId}, removing job: ${jobName}`);
        await this.autoActionQueue.removeAutoActionJob(userId, targetId, type);
        return;
      }

      // Check if auto mode has expired (for work mode)
      if (type === AutoMode.WORK && user.autoModeExpiresAt && new Date() > user.autoModeExpiresAt) {
        this.logger.log(`Auto work for user ${userId} has expired, removing job: ${jobName}`);
        await this.autoActionQueue.removeAutoActionJob(userId, targetId, type);
        await this.userService.updateAutoMode(userId, {
          activeAutoMode: AutoMode.NONE,
          autoTargetId: null,
          autoModeExpiresAt: null,
        });
        return;
      }

      // Calculate current energy
      const currentEnergy = this.energyService.calculateCurrentEnergy(user);

      // Check if user has minimum required energy
      if (currentEnergy < this.MINIMUM_ENERGY_THRESHOLD) {
        this.logger.log(`User ${userId} has insufficient energy (${currentEnergy}), skipping execution`);
        return;
      }

      // Execute the appropriate action
      if (type === AutoMode.WORK) {
        await this.executeAutoWork(userId, targetId, currentEnergy);
      } else if (type === AutoMode.WAR) {
        await this.executeAutoWar(userId, targetId, currentEnergy);
      }

      this.logger.log(`Successfully executed auto ${type} for user ${userId}`);
    } catch (error) {
      this.logger.error(`Error processing auto action job: ${jobName}`, error);

      // If it's a critical error (like target not found), remove the job
      if (this.isCriticalError(error)) {
        this.logger.warn(`Critical error detected, removing job: ${jobName}`);
        await this.autoActionQueue.removeAutoActionJob(userId, targetId, type);
      }

      throw error; // Re-throw to trigger retry mechanism
    }
  }

  private async executeAutoWork(userId: number, factoryId: string, energyAmount: number): Promise<void> {
    try {
      this.logger.log(`Auto work execution requested for user ${userId} at factory ${factoryId} with ${energyAmount} energy`);

      // For now, we'll just log the execution request
      // The actual execution will be handled by the existing services
      // This is a placeholder until we resolve the circular dependency
      this.logger.log(`Auto work would execute with ${energyAmount} energy - implementation pending`);

    } catch (error) {
      this.logger.error(`Auto work failed for user ${userId} at factory ${factoryId}: ${error.message}`);
      throw error;
    }
  }

  private async executeAutoWar(userId: number, warId: string, energyAmount: number): Promise<void> {
    try {
      this.logger.log(`Auto war execution requested for user ${userId} in war ${warId} with ${energyAmount} energy`);

      // For now, we'll just log the execution request
      // The actual execution will be handled by the existing services
      // This is a placeholder until we resolve the circular dependency
      this.logger.log(`Auto war would execute with ${energyAmount} energy - implementation pending`);

    } catch (error) {
      this.logger.error(`Auto war attack failed for user ${userId} in war ${warId}: ${error.message}`);
      throw error;
    }
  }

  private isCriticalError(error: any): boolean {
    const criticalMessages = [
      'Factory not found',
      'War not found',
      'User not found',
      'Factory does not exist',
      'War does not exist',
      'User is not in the same region',
      'Factory is not in user region',
    ];

    return criticalMessages.some(message =>
      error.message && error.message.includes(message)
    );
  }
}
