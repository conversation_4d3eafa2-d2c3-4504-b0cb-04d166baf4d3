import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Injectable, Logger, Inject, forwardRef } from '@nestjs/common';
import { Job } from 'bullmq';
import { AutoActionJobData } from './dto/auto-action-job.dto';
import { AutoMode } from '../user/enums/auto-mode.enum';
import { UserService } from '../user/user.service';
import { EnergyService } from '../user/energy.service';
import { AutoActionQueue } from './auto-action.queue';
import { ModuleRef } from '@nestjs/core';
import { WarStatus } from '../war/entity/war.entity';

@Injectable()
@Processor('auto-actions')
export class AutoActionWorker extends WorkerHost {
  private readonly logger = new Logger(AutoActionWorker.name);
  private readonly MINIMUM_ENERGY_THRESHOLD = 10;

  constructor(
    @Inject(forwardRef(() => UserService))
    private readonly userService: UserService,
    @Inject(forwardRef(() => EnergyService))
    private readonly energyService: EnergyService,
    private readonly autoActionQueue: AutoActionQueue,
    private readonly moduleRef: ModuleRef,
  ) {
    super();
  }

  async process(job: Job<AutoActionJobData>): Promise<void> {
    const { userId, targetId, type } = job.data;
    const jobName = `${userId}_${targetId}_${type}`;

    this.logger.log(`Processing auto action job: ${jobName}`);

    try {
      // Check if user still exists and is premium
      const user = await this.userService.findOne(userId);
      if (!user) {
        this.logger.warn(`User ${userId} not found, removing job: ${jobName}`);
        await this.autoActionQueue.removeAutoActionJob(userId, targetId, type);
        return;
      }

      if (!user.isPremium) {
        this.logger.warn(`User ${userId} is no longer premium, removing job: ${jobName}`);
        await this.autoActionQueue.removeAutoActionJob(userId, targetId, type);
        return;
      }

      // Check if auto mode is still active for this user
      if (user.activeAutoMode !== type || user.autoTargetId !== targetId) {
        this.logger.log(`Auto mode changed for user ${userId}, removing job: ${jobName}`);
        await this.autoActionQueue.removeAutoActionJob(userId, targetId, type);
        return;
      }

      // Check if auto mode has expired (for work mode)
      if (type === AutoMode.WORK && user.autoModeExpiresAt && new Date() > user.autoModeExpiresAt) {
        this.logger.log(`Auto work for user ${userId} has expired, removing job: ${jobName}`);
        await this.autoActionQueue.removeAutoActionJob(userId, targetId, type);
        await this.userService.updateAutoMode(userId, {
          activeAutoMode: AutoMode.NONE,
          autoTargetId: null,
          autoModeExpiresAt: null,
        });
        return;
      }

      // Calculate current energy
      const currentEnergy = this.energyService.calculateCurrentEnergy(user);

      // Check if user has minimum required energy
      if (currentEnergy < this.MINIMUM_ENERGY_THRESHOLD) {
        this.logger.log(`User ${userId} has insufficient energy (${currentEnergy}), skipping execution`);
        return;
      }

      // Execute the appropriate action
      if (type === AutoMode.WORK) {
        await this.executeAutoWork(userId, targetId, currentEnergy);
      } else if (type === AutoMode.WAR) {
        await this.executeAutoWar(userId, targetId, currentEnergy);
      }

      this.logger.log(`Successfully executed auto ${type} for user ${userId}`);
    } catch (error) {
      this.logger.error(`Error processing auto action job: ${jobName}`, error);

      // If it's a critical error (like target not found), remove the job
      if (this.isCriticalError(error)) {
        this.logger.warn(`Critical error detected, removing job: ${jobName}`);
        await this.autoActionQueue.removeAutoActionJob(userId, targetId, type);
      }

      throw error; // Re-throw to trigger retry mechanism
    }
  }

  private async executeAutoWork(userId: number, factoryId: string, energyAmount: number): Promise<void> {
    try {
      this.logger.log(`Executing auto work for user ${userId} at factory ${factoryId} with ${energyAmount} energy`);

      // Dynamically resolve FactoryService to avoid circular dependency
      const { FactoryService } = await import('../factory/factory.service');
      const factoryService = this.moduleRef.get(FactoryService, { strict: false });

      if (!factoryService) {
        throw new Error('FactoryService not available');
      }

      // Work at the factory
      await factoryService.workAtFactory(+factoryId, userId, {
        energySpent: energyAmount,
      });

      this.logger.log(`Auto work successful for user ${userId} at factory ${factoryId}`);
    } catch (error) {
      this.logger.error(`Auto work failed for user ${userId} at factory ${factoryId}: ${error.message}`);
      throw error;
    }
  }

  private async executeAutoWar(userId: number, warId: string, energyAmount: number): Promise<void> {
    try {
      this.logger.log(`Executing auto war attack for user ${userId} in war ${warId} with ${energyAmount} energy`);

      // Dynamically resolve WarService to avoid circular dependency
      const { WarService } = await import('../war/war.service');
      const warService = this.moduleRef.get(WarService, { strict: false });

      if (!warService) {
        throw new Error('WarService not available');
      }

      // Check if war is still active (GROUND_PHASE is the active phase)
      const war = await warService.findWarById(warId);
      if (!war || war.status !== WarStatus.GROUND_PHASE) {
        this.logger.log(`War ${warId} is no longer active (status: ${war?.status}), stopping auto attack for user ${userId}`);
        await this.autoActionQueue.removeAutoActionJob(userId, warId, AutoMode.WAR);
        await this.userService.updateAutoMode(userId, {
          activeAutoMode: AutoMode.NONE,
          autoTargetId: null,
          autoModeExpiresAt: null,
        });
        return;
      }

      // Create participation DTO for the war service
      const participateDto = {
        energyAmount: energyAmount,
        autoMode: false, // Don't trigger auto mode recursively
        autoEnergyPercentage: 100,
      };

      // Participate in the war (correct parameter order: userId, warId, dto)
      await warService.participateInWar(userId, warId, participateDto);

      this.logger.log(`Auto war attack successful for user ${userId} in war ${warId}`);
    } catch (error) {
      this.logger.error(`Auto war attack failed for user ${userId} in war ${warId}: ${error.message}`);
      throw error;
    }
  }

  private isCriticalError(error: any): boolean {
    const criticalMessages = [
      'Factory not found',
      'War not found',
      'User not found',
      'Factory does not exist',
      'War does not exist',
      'User is not in the same region',
      'Factory is not in user region',
      'FactoryService not available',
      'WarService not available',
      'Can only participate in wars during the ground phase',
      'War is already ended',
      'Factory is not in user region',
      'You can only participate in wars involving your region',
      'You are already traveling',
      'Auto mode is only available for premium users',
    ];

    return criticalMessages.some(message =>
      error.message && error.message.includes(message)
    );
  }
}
