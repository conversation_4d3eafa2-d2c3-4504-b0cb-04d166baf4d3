{"name": "wn-server", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "typeorm": "ts-node ./node_modules/typeorm/cli", "migration:run": "npm run typeorm migration:run -- -d ./src/database/typeorm.ts", "migration:generate": "npm run typeorm -- -d ./src/database/typeorm.ts migration:generate ./src/database/migrations/%npm_config_name%", "migration:create": "npm run typeorm -- migration:create ./src/database/migrations/%npm_config_name%", "migration:revert": "npm run typeorm -- -d ./src/database/typeorm.ts migration:revert", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs-modules/mailer": "^2.0.2", "@nestjs/axios": "^4.0.0", "@nestjs/bullmq": "^11.0.2", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.1", "@nestjs/core": "^11.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.1", "@nestjs/platform-socket.io": "^11.0.1", "@nestjs/schedule": "^5.0.1", "@nestjs/swagger": "^11.0.6", "@nestjs/throttler": "^6.2.1", "@nestjs/typeorm": "^11.0.0", "@nestjs/websockets": "^11.0.1", "@types/multer": "^1.4.12", "@types/nodemailer": "^6.4.17", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "bullmq": "^5.36.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cookie-parser": "^1.4.7", "cron": "^4.3.0", "dompurify": "^3.2.3", "handlebars": "^4.7.8", "helmet": "^8.0.0", "ioredis": "^5.4.1", "jsdom": "^25.0.1", "minio": "^8.0.5", "multer": "^2.0.0", "nodemailer": "^6.10.0", "passport": "^0.7.0", "pg": "^8.14.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "sharp": "^0.34.2", "socket.io": "^4.8.1", "stripe": "^18.0.0", "typeorm": "^0.3.21"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/bcrypt": "^5.0.2", "@types/bcryptjs": "^2.4.6", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/jsdom": "^21.1.7", "@types/node": "^22.10.7", "@types/supertest": "^6.0.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}