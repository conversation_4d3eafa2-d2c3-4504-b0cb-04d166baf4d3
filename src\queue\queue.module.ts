import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bullmq';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AutoActionQueue } from './auto-action.queue';
import { AutoActionWorker } from './auto-action.worker';
import { UserModule } from '../user/user.module';
import { FactoryModule } from '../factory/factory.module';
import { WarModule } from '../war/war.module';

@Module({
  imports: [
    BullModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        connection: {
          host: configService.get('REDIS_HOST') || 'localhost',
          port: configService.get('REDIS_PORT') || 6379,
          password: configService.get('REDIS_PASSWORD') || undefined,
          db: configService.get('REDIS_DB') || 0,
        },
        defaultJobOptions: {
          removeOnComplete: 10,
          removeOnFail: 50,
        },
      }),
      inject: [ConfigService],
    }),
    BullModule.registerQueue({
      name: 'auto-actions',
    }),
    UserModule,
    FactoryModule,
    WarModule,
  ],
  providers: [AutoActionQueue, AutoActionWorker],
  exports: [AutoActionQueue],
})
export class QueueModule {}
