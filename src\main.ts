import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from '@nestjs/common';
import helmet from 'helmet';
import * as cookieParser from 'cookie-parser';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { NestExpressApplication } from '@nestjs/platform-express';
import * as bodyParser from 'body-parser';

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule, {
    bodyParser: false, // Disable the default body parser
  });

  // Parse JSON requests except for webhook route
  app.use((req, res, next) => {
    if (req.originalUrl === '/payments/webhook') {
      // For webhook requests, use raw body parser to get the raw body for signature verification
      bodyParser.raw({ type: 'application/json' })(req, res, (err) => {
        if (err) return next(err);
        // Store a reference to the raw body for signature verification
        req['rawBody'] = req.body;
        next();
      });
    } else {
      // For all other requests, use the standard JSON body parser
      bodyParser.json()(req, res, next);
    }
  });

  app.use(cookieParser());
  app.use(helmet());
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
    }),
  );

  app.enableCors({
    origin: [process.env.CLIENT_URL || 'http://localhost:3000'],
    credentials: true,
  });

  const config = new DocumentBuilder()
    .setTitle('Warfront Nations')
    .setDescription('This is the API for Warfront Nations application')
    .setVersion('1.0')
    .addBearerAuth()
    .addSecurityRequirements('bearer')
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('docs', app, document);

  await app.listen(process.env.PORT ?? 3000);
}
bootstrap();
